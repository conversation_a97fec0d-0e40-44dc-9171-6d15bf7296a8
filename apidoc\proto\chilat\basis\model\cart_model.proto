syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "common.proto";
import "common/business.proto";
import "chilat/basis/model/goods_model.proto";
import "chilat/basis/model/goods_looking_model.proto";
import "chilat/commodity/commodity_common.proto";

// ----------------------      1. 购物车数据     ----------------------

// 购物车统计数据
message MidCartStatResp {
  common.Result result = 1;
  MidCartStatModel data = 2;
}

// 购物车统计数据
message MidCartStatModel {
  int32 onlineSkuCount = 1; //上架SKU数量
  int32 selectSkuCount = 2; //选择的SKU行数
  int32 goodsCount = 3; //商品种类数
  int32 skuCount = 4; //sku种类数
  double totalSalePrice = 5; //总销售额
  int32 selectSkuTotalQuantity = 6; //选择的SKU总数
  double selectTotalSalePrice = 7; //总选择销售额
  double selectTotalCommission = 80; //选择商品的总佣金（基于购物车中，已选中商品的总金额计算）
}

//获取购物车信息（按Tab分割）
message MidCartByTabResp {
  common.Result result = 1;
  MidCartByTabModel data = 2;
}

//获取购物车信息（按Tab分割）的对象
message MidCartByTabModel {
  MidCartStatModel stat = 10; //购物车汇总统计
  MidCartTabInfoModel onlineOrderCartTab = 50; //在线订单购物车Tab
  MidCartTabInfoModel goodsLookingCartTab = 60; //询盘单购物车Tab
  InquiryModel lastInquiry = 100; //上次询盘信息
}

//购物车Tab信息
message MidCartTabInfoModel {
  MidCartStatModel stat = 1;
  repeated MidCartGoodsModel goodsList = 2;
  double totalEstimateFreight = 181; //预估运费总计（全部SKU可预估运费时，才返回）
  double partEstimateFreight = 182; //预估运费部分（只要有一个SKU可预估运费时，则返回）
  int32 freightGoodsQty = 183; //参与预估运费的商品数量
}

message MidCartResp {
  common.Result result = 1;
  MidCartModel data = 2;
}

message MidCartModel {
  MidCartStatModel stat = 1;
  repeated MidCartGoodsModel goodsList = 2;
  // 上次询盘信息
  InquiryModel lastInquiry = 3;
  double totalEstimateFreight = 181; //预估运费总计（全部SKU可预估运费时，才返回）
  double partEstimateFreight = 182; //预估运费部分（只要有一个SKU可预估运费时，则返回）
  int32 freightGoodsQty = 183; //参与预估运费的商品数量
}

message MidCartGoodsModel {
  string goodsId = 1; //商品ID
  string goodsNo = 2; // 商品编号-主数据
  string goodsName = 3; // 商品名称-主数据
  string goodsTitle = 4; // 商品标题-主数据
  int32 minBuyQuantity = 5; //最小购买数量（起订量）
  string goodsPriceUnitName = 6; // 商品计价单位名称
  int32 minIncreaseQuantity = 7; //最小加购数量
  string mainImageUrl = 8; //商品主图
  int32 selectedGoodsQty = 180; //选中的商品数量
  repeated MidCartSkuModel skuList = 9; // 商品SKU列表
  repeated commodity.GoodsExtendItem specItemList = 10; // 规格参数
  double estimateFreight = 181; //预估运费
  bool supportOnlineOrder = 300; //支持在线订单（商城前台可直接下单的商品）
  string routeId = 190; //线路ID
  string padc = 88; //促销活动动态代码（与goodsId一起，组成虚拟商品）
  string paName = 89; //促销活动名称（活动显示名称）
}

// 商品SKU信息
message MidCartSkuModel {
  string eid = 80; //数据表实体ID
  string skuId = 1; // SKU ID（必填）
  string skuNo = 2; // SKU商品料号（必填）
  repeated commodity.GoodsExtendItem specItemList = 3;
  int32 stockQty = 4; // 库存数量
  bool selected = 5; // 是否已选中
  double salePrice = 6; // 销售价
  int32 buyQty = 7; // 加购数量
  double subtotalSalePrice = 9; // 销售价小计
  string skuImage = 10; //商品图片
  string goodsId = 11; //商品ID
  repeated common.SkuStepPrice stepPrices = 12; //阶梯价
  string spm = 13; //SPM跟踪码
  int32 minIncreaseQuantity = 40; //最小加购数量（SKU维度一次加购数量，若值为null，则表示取SPU的minIncreaseQuantity）
  double estimateFreight = 181; //预估运费
  string padc = 88; //促销活动动态代码（仅促销活动的padc，加padc后组成唯一KEY）
}

// 商品购物车中的SKU数量
message MidGoodsCartInfoModel {
  string routeId = 10; //线路ID
  map<string, int32> skuQtyMap = 20; //SKU数量（Key: skuId，Value: quantity）
}
